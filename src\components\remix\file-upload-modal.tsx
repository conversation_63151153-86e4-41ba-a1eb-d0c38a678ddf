'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle, VisuallyHidden } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Play, Pause, Trash2, Disc } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRemixModalTheme } from '@/app/attention/utils/modal-theme';
import {
  UploadFile,
  validateFiles,
  createUploadFiles,
  uploadFile as uploadFileUtil,
  formatFileSize,
  formatDuration,
  createAudioUrl,
  cleanupAudioUrl
} from '@/lib/file-upload-utils';
import Image from 'next/image';
import { useMutation, useLazyQuery } from '@apollo/client';
import { CREATE_APPLICATIONS_MUTATION, GET_STEM_UPLOAD_URL } from '@/graphql/remix-queries';
import { useAuth } from '@/contexts/auth/auth-context';


interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (files: UploadFile[]) => void;
}

export function FileUploadModal({ isOpen, onClose, onSubmit }: FileUploadModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [applicationMessage, setApplicationMessage] = useState('');
  const [title, setTitle] = useState('');
  const [playingFileId, setPlayingFileId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [fileDurations, setFileDurations] = useState<Map<string, number>>(new Map());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiMessage, setApiMessage] = useState<string | null>(null); // For API error/success messages
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get remix theme for modal
  const { cssVariables, themeMode } = useRemixModalTheme();

  // Auth context
  const { user } = useAuth();

  // GraphQL mutation for creating applications
  const [createApplication] = useMutation(CREATE_APPLICATIONS_MUTATION);
  const [getStemPresignUrl] = useLazyQuery(GET_STEM_UPLOAD_URL, { fetchPolicy: 'no-cache' });



  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);


  // Move startUploading above handleFiles to avoid use-before-assign
  // Remove duplicate startUploading definition


  // Move startUploading above handleFiles to fix initialization order
  const startUploading = React.useCallback(async (filesToUpload: UploadFile[]) => {
    setIsUploading(true);

    for (const uploadFile of filesToUpload) {
      // Get audio duration
      const audio = new Audio();
      const audioUrl = createAudioUrl(uploadFile.file);
      audio.src = audioUrl;
      audio.addEventListener('loadedmetadata', () => {
        setFileDurations(prev => new Map(prev.set(uploadFile.id, audio.duration)));
        cleanupAudioUrl(audioUrl);
      });

      try {
        // Update status to uploading
        setUploadFiles(prev =>
          prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'uploading', progress: 0 }
              : f
          )
        );

        // Get presigned URL from GraphQL
        const { data: presignData } = await getStemPresignUrl({
          variables: {
            fileName: uploadFile.name,
            contentType: uploadFile.type,
            gigId: "de2e4a11-9b9c-4fe0-9c95-1fb2172e69b4", // Replace with dynamic gigId if needed
          },
        });
        const presignedUrl = presignData?.getApplicationMediaUploadUrl?.uploadUrl;
        const viewUrl = presignData?.getApplicationMediaUploadUrl?.viewUrl;

        if (!presignedUrl || !viewUrl) {
          throw new Error('Failed to get presigned URL or view URL');
        }

        const result = await uploadFileUtil(
          uploadFile,
          (progress: number) => {
            setUploadFiles(prev =>
              prev.map(f =>
                f.id === uploadFile.id
                  ? { ...f, progress }
                  : f
              )
            );
          },
          presignedUrl
        );

        setUploadFiles(prev =>
          prev.map(f =>
            f.id === uploadFile.id
              ? {
                  ...f,
                  status: result.success ? 'completed' : 'error',
                  url: result.success ? viewUrl : undefined, // Always set url to viewUrl for playback
                  error: result.error,
                  progress: result.success ? 100 : 0,
                }
              : f
          )
        );
      } catch (error) {
        console.error('Upload error:', error);
        setUploadFiles(prev =>
          prev.map(f =>
            f.id === uploadFile.id
              ? {
                  ...f,
                  status: 'error',
                  error: 'Upload failed',
                  progress: 0,
                }
              : f
          )
        );
      }
    }

    setIsUploading(false);
  }, [getStemPresignUrl]);

  // Now define handleFiles after startUploading
  const handleFiles = React.useCallback((files: File[]) => {
    const { validFiles, errors: validationErrors } = validateFiles(files, uploadFiles);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors([]);
    const newUploadFiles = createUploadFiles(validFiles);
    setUploadFiles(prev => [...prev, ...newUploadFiles]);

    // Start uploading files and get durations
    startUploading(newUploadFiles);
  }, [uploadFiles, startUploading]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  }, [handleFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
    
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  const handleRetryUpload = useCallback(async (uploadFile: UploadFile) => {
    if (isUploading) return;

    try {
      // Update status to uploading
      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'uploading', progress: 0, error: undefined }
            : f
        )
      );

      // Get presigned URL from GraphQL for retry
      const { data: presignData } = await getStemPresignUrl({
        variables: {
          fileName: uploadFile.name,
          contentType: uploadFile.type,
          gigId: "de2e4a11-9b9c-4fe0-9c95-1fb2172e69b4", // Replace with dynamic gigId if needed
        },
      });
      const presignedUrl = presignData?.getApplicationMediaUploadUrl?.uploadUrl;
      const viewUrl = presignData?.getApplicationMediaUploadUrl?.viewUrl;

      if (!presignedUrl || !viewUrl) {
        throw new Error('Failed to get presigned URL or view URL');
      }

      const result = await uploadFileUtil(
        uploadFile,
        (progress: number) => {
          setUploadFiles(prev =>
            prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, progress }
                : f
            )
          );
        },
        presignedUrl
      );

      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? {
                ...f,
                status: result.success ? 'completed' : 'error',
                url: result.success ? viewUrl : undefined,
                error: result.error,
                progress: result.success ? 100 : 0,
              }
            : f
        )
      );
    } catch (error) {
      console.error('Retry upload error:', error);
      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? {
                ...f,
                status: 'error',
                error: 'Upload failed',
                progress: 0,
              }
            : f
        )
      );
    }
  }, [isUploading, getStemPresignUrl]);

  const handleRemoveFile = useCallback((fileId: string) => {
    // Stop audio if playing
    if (playingFileId === fileId) {
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingFileId(null);
    }
    
    // Remove from state
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
    setFileDurations(prev => {
      const newMap = new Map(prev);
      newMap.delete(fileId);
      return newMap;
    });
    
    // Cleanup audio element
    const audio = audioElements.get(fileId);
    if (audio) {
      const audioUrl = audio.src;
      audio.pause();
      // Only cleanup local URLs, not S3 URLs
      if (audioUrl.startsWith('blob:')) {
        cleanupAudioUrl(audioUrl);
      }
      setAudioElements(prev => {
        const newMap = new Map(prev);
        newMap.delete(fileId);
        return newMap;
      });
    }
  }, [playingFileId, audioElements]);

  const handlePlayPause = useCallback((fileId: string, file: File) => {
    if (playingFileId === fileId) {
      // Pause current file
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
      }
      setPlayingFileId(null);
    } else {
      // Stop any currently playing audio
      if (playingFileId) {
        const currentAudio = audioElements.get(playingFileId);
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }
      }

      // Play new file
      let audio = audioElements.get(fileId);
      if (!audio) {
        audio = new Audio();
        // Use S3 URL if available, otherwise create local URL
        const uploadFile = uploadFiles.find(f => f.id === fileId);
        const audioUrl = uploadFile?.url || createAudioUrl(file);
        audio.src = audioUrl;
        
        audio.addEventListener('ended', () => {
          setPlayingFileId(null);
        });
        
        setAudioElements(prev => new Map(prev.set(fileId, audio!)));
      }
      
      audio.play();
      setPlayingFileId(fileId);
    }
  }, [playingFileId, audioElements, uploadFiles]);

  // const handleDownload = useCallback((uploadFile: UploadFile) => {
  //   // Use S3 URL if available, otherwise create local URL
  //   const url = uploadFile.url || createAudioUrl(uploadFile.file);
  //   const link = document.createElement('a');
  //   link.href = url;
  //   link.download = uploadFile.name;
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
    
  //   // Only cleanup if it's a local URL
  //   if (!uploadFile.url) {
  //     cleanupAudioUrl(url);
  //   }
  // }, []);

  const handleSubmit = async () => {
    setApiMessage(null);
    const completedFiles = uploadFiles.filter(f => f.status === 'completed');
    if (completedFiles.length > 0 && user?.email) {
      setIsSubmitting(true);
      try {
        // Get the first completed file (since we only allow one file)
        const uploadedFile = completedFiles[0];
        // Create the application using GraphQL mutation
        const result = await createApplication({
          variables: {
            personalNote: applicationMessage || "Don't get too personal",
            userEmail: user.email,
            mediaTitle: title || uploadedFile.name,
            mediaUrl: uploadedFile.url || "",
            gigId: "de2e4a11-9b9c-4fe0-9c95-1fb2172e69b4"
          }
        });

        // Check for GraphQL errors
        if (result.errors && result.errors.length > 0) {
          // Show the first error message under the song div
          setApiMessage(result.errors[0].message || 'Submission failed.');
          return;
        }

        if (result.data?.createApplications?.info?.nodesCreated > 0) {
          setIsSubmitted(true);
          setApiMessage('Your application was submitted successfully!');
          // Call onSubmit with completed files after a small delay to ensure state update
          setTimeout(() => {
            onSubmit(completedFiles);
          }, 100);
        } else {
          setApiMessage('Failed to create application.');
        }
      } catch (error) {
        // Try to type error as unknown and check for GraphQL error shape
        const err = error as unknown;
        if (
          typeof err === 'object' &&
          err !== null &&
          'graphQLErrors' in err &&
          Array.isArray((err as { graphQLErrors: { message?: string }[] }).graphQLErrors) &&
          ((err as { graphQLErrors: { message?: string }[] }).graphQLErrors.length > 0)
        ) {
          setApiMessage((err as { graphQLErrors: { message?: string }[] }).graphQLErrors[0].message || 'Submission failed.');
        } else if (typeof err === 'object' && err !== null && 'message' in err) {
          setApiMessage((err as { message?: string }).message || 'Submission failed.');
        } else {
          setApiMessage('Failed to submit application. Please try again.');
        }
        console.error('Error submitting application:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // handleDone function removed as it's not currently used
  // const handleDone = () => {
  //   const completedFiles = uploadFiles.filter(f => f.status === 'completed');
  //   onSubmit(completedFiles);
  //   handleClose();
  // };

  const handleClose = () => {
    // Cleanup all audio elements
    audioElements.forEach((audio) => {
      audio.pause();
      const audioUrl = audio.src;
      // Only cleanup local URLs, not S3 URLs
      if (audioUrl.startsWith('blob:')) {
        cleanupAudioUrl(audioUrl);
      }
    });

    setUploadFiles([]);
    setErrors([]);
    setIsUploading(false);
    setApplicationMessage('');
    setTitle('');
    setPlayingFileId(null);
    setAudioElements(new Map());
    setFileDurations(new Map());
    setIsSubmitted(false);
    setIsSubmitting(false);
    onClose();
  };

  const completedFiles = uploadFiles.filter(f => f.status === 'completed');
  const uploadingFiles = uploadFiles.filter(f => f.status === 'uploading');
  const canSubmit = completedFiles.length > 0 && !isUploading && uploadingFiles.length === 0 && title.trim().length > 0 && !isSubmitting;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="remix-theme-scope max-w-[calc(100%-2rem)] sm:max-w-2xl flex flex-col p-0 overflow-hidden"
        style={cssVariables}
        data-theme={themeMode}
      >
        <VisuallyHidden>
          <DialogTitle>Apply for Open Verse Contest</DialogTitle>
        </VisuallyHidden>
        {isSubmitted ? (
          /* Success Screen */
          <div className="flex flex-col items-center justify-center p-6 sm:p-12 text-center space-y-6">
            <div className=" rounded-full flex items-center justify-center">
              {/* <CheckCircle className="h-8 w-8 text-white" /> */}
              <Image
                                                    src="/check.svg"
            alt="check"
                                                    width={150}
                                                    height={150}
                                                    className="object-contain w-full h-full relative z-10"
                                                  />
            </div>
            <div className="space-y-2">
              <h2 className="text-xl font-bold text-foreground">
                Yay, Your application is submitted successfully
              </h2>
              <p className="text-lg text-muted-foreground max-w-md font-secondary leading-8">
                Thank you for submitting your ATTENTION verse! We&apos;re excited to review your submission and will get back to you soon. Stay tuned and keep making music!
              </p>
            </div>
            {/* <Button
              onClick={handleClose}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8"
            >
              Done
            </Button> */}
          </div>
        ) : (
          <>
            {/* Header - Hidden on mobile, shown on desktop */}
            <div className="hidden sm:flex items-center justify-between p-6 border-b border-border sticky top-0 bg-background z-10">
              <div className="flex flex-col items-start gap-3">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground text-lg font-bold"><Disc/></span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-foreground">Apply for Open Verse Contest</h2>
                  <p className="text-sm text-muted-foreground font-secondary">Upload your recording to SMASH and share it with the world.</p>
                </div>
              </div>
            </div>

            {/* Mobile Header - Compact version for mobile */}
            <div className="sm:hidden flex items-center justify-between p-4 border-b border-border sticky top-0 bg-background z-10">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground text-sm font-bold"><Disc/></span>
                </div>
                <div>
                  <h2 className="text-lg font-bold text-foreground">Apply for Open Verse Contest</h2>
                  <p className="text-xs text-muted-foreground">Upload your recording to SMASH and share it with the world.</p>
                </div>
              </div>
            </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Title Field */}
          <div className="flex flex-col justify-start items-start gap-1.5">
            <label className="text-sm font-bold  text-foreground">
              Title *
            </label>
            <Input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Specify the title for submission"
              className="w-full px-4 py-2.5 bg-white rounded border border-border text-sm font-secondary text-foreground placeholder:text-muted-foreground placeholder:font-secondary focus:border-primary focus:ring-1 focus:ring-primary"
            />
          </div>

          {/* Application Message */}
          <div className="space-y-2">
            <label className="text-sm font-bold text-foreground">Application Message</label>
            <Textarea
              placeholder="Tell the artist why you're perfect for this gig"
              value={applicationMessage}
              onChange={(e) => {
                if (e.target.value.length <= 1000) {
                  setApplicationMessage(e.target.value);
                }
              }}
              className="min-h-[80px] sm:min-h-[100px] resize-none border-input focus:border-ring focus:ring-ring font-secondary placeholder:font-secondary"
              maxLength={1000}
            />
            <div className="text-xs text-muted-foreground text-right font-secondary">
              {applicationMessage.length}/1000 characters
            </div>
          </div>

          {/* Upload Section */}
          <div className="space-y-2">
            <label className="text-sm font-bold text-foreground">Upload your Remix File</label>

            {/* Upload Area */}
          {uploadFiles.length <= 0  &&  <div
              className={cn(
                "border-2 border-dashed rounded-lg p-4 sm:p-8 text-center transition-colors cursor-pointer",
                isDragOver
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center gap-3 sm:gap-4">
                <Upload className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-sm sm:text-base text-muted-foreground font-secondary">
                    Drag & drop your audio file or click to browse
                  </p>
                  <Button
                    className="cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground px-4 sm:px-6 text-sm"
                  >
                    Select File
                  </Button>
                  <p className="text-xs text-muted-foreground font-secondary">
                    Supports: AIF, AIFF, WAV, MP3, MP4, AAC
                  </p>
                </div>
              </div>
            </div>}
          </div>

          {/* File List */}
          {uploadFiles.length > 0 && (
            <>
              <div className="space-y-3">
                {uploadFiles.map((uploadFile) => {
                  const duration = fileDurations.get(uploadFile.id);
                  const isPlaying = playingFileId === uploadFile.id;
                  return (
                    <div
                      key={uploadFile.id}
                      className="flex items-center gap-3 p-3 border border-border rounded-lg"
                    >
                      {/* File Icon */}
                      <div className="w-10 h-10 bg-primary rounded flex items-center justify-center flex-shrink-0">
                        <span className="text-primary-foreground text-xs font-bold">
                          {uploadFile.name.split('.').pop()?.toUpperCase()}
                        </span>
                      </div>
                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-foreground truncate">
                            {uploadFile.name}
                          </h4>
                          <div className="flex items-center gap-2 ml-2">
                            <span className="text-xs text-muted-foreground">
                              {formatFileSize(uploadFile.file.size)}
                            </span>
                            {duration && (
                              <>
                                <span className="text-xs text-muted-foreground">•</span>
                                <span className="text-xs text-muted-foreground">
                                  {formatDuration(duration)}
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                        {/* Upload Progress */}
                        {uploadFile.status === 'uploading' && (
                          <div className="mt-2">
                            <div className="w-full bg-muted rounded-full h-1.5">
                              <div 
                                className="bg-primary h-1.5 rounded-full transition-all duration-300"
                                style={{ width: `${uploadFile.progress}%` }}
                              />
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              {uploadFile.progress}% uploaded
                            </p>
                          </div>
                        )}
                        {/* Error Message */}
                        {uploadFile.status === 'error' && uploadFile.error && (
                          <div className="mt-2">
                            <p className="text-xs text-destructive">
                              {uploadFile.error}
                            </p>
                          </div>
                        )}
                      </div>
                      {/* Actions */}
                      <div className="flex items-center gap-1">
                        {/* Play/Pause Button */}
                        {(uploadFile.status === 'completed' || uploadFile.status === 'pending') && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePlayPause(uploadFile.id, uploadFile.file)}
                            className="h-8 w-8 p-0 text-primary"
                          >
                            {isPlaying ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        {/* Retry Button for failed uploads */}
                        {uploadFile.status === 'error' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRetryUpload(uploadFile)}
                            className="h-8 w-8 p-0 text-primary hover:text-primary/80"
                            disabled={isUploading}
                          >
                            <Upload className="h-4 w-4" />
                          </Button>
                        )}
                        {/* Delete Button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveFile(uploadFile.id)}
                          className="h-8 w-8 p-0 text-muted-foreground"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
              {/* API error/success message below song div */}
              {apiMessage && (
                <div className={`mt-3 text-center text-sm ${apiMessage.toLowerCase().includes('success') ? 'text-green-600 bg-green-50 border border-green-200' : 'text-destructive bg-destructive/10 border border-destructive/20'} p-3 rounded-lg`}>
                  {apiMessage}
                </div>
              )}
            </>
          )}

          {/* Errors */}
          {errors.length > 0 && (
            <div className="space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-destructive bg-destructive/10 border border-destructive/20 p-3 rounded-lg">
                  {error}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-center sm:justify-end gap-3 p-4 sm:p-6 border-t border-border bg-muted/30 sticky bottom-0 z-10">
          <Button
            variant="outline"
            onClick={handleClose}
            className="px-4 sm:px-6 flex-1 sm:flex-none"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 sm:px-6 flex-1 sm:flex-none"
          >
            {isSubmitting ? 'Submitting...' : uploadingFiles.length > 0 ? 'Uploading...' : 'Submit Track'}
          </Button>
        </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".aif,.aiff,.wav,.mp3,.mp4,.aac,audio/wav,audio/mpeg,audio/mp3,audio/aiff,audio/x-aiff,audio/mp4,audio/aac"
              onChange={handleFileSelect}
              className="hidden"
            />
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
