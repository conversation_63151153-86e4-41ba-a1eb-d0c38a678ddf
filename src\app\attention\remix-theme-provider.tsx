'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { remixThemeConfig, RemixThemeMode } from './theme-config';

interface RemixThemeContextType {
  mode: RemixThemeMode;
}

const RemixThemeContext = createContext<RemixThemeContextType | undefined>(undefined);

export function useRemixTheme() {
  const context = useContext(RemixThemeContext);
  if (context === undefined) {
    throw new Error('useRemixTheme must be used within a RemixThemeProvider');
  }
  return context;
}

interface RemixThemeProviderProps {
  children: React.ReactNode;
}

export function RemixThemeProvider({ children }: RemixThemeProviderProps) {
  // const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Force light theme on document root for remix flow
    const root = document.documentElement;
    // const originalClasses = Array.from(root.classList);
    root.classList.remove('dark', 'system');
    root.classList.add('light');

    // Cleanup: restore original theme when component unmounts
    return () => {
      // Get the current theme from localStorage to restore
      const savedTheme = localStorage.getItem('theme') || 'system';
      root.classList.remove('light', 'dark', 'system');

      if (savedTheme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        root.classList.add(systemTheme);
      } else {
        root.classList.add(savedTheme);
      }
    };
  }, []);

  // Force light theme mode for remix flow
  const mode: RemixThemeMode = 'light';
    // const mode: RemixThemeMode = resolvedTheme === 'dark' ? 'dark' : 'light';

  const themeColors = remixThemeConfig[mode];

  // Generate CSS custom properties for the current theme
  const cssVariables = React.useMemo(() => {
    const vars: Record<string, string> = {};

    // Font families for remix pages
    vars['--font-primary'] = `'Zilla Slab', serif`;
    vars['--font-secondary'] = `'Inter', sans-serif`;
    // Default font-family for the whole remix flow
    vars['--font-family'] = `var(--font-primary)`;

    // Primary colors
    vars['--primary'] = `hsl(${themeColors.primary.DEFAULT})`;
    vars['--primary-foreground'] = `hsl(${themeColors.primary.foreground})`;

    // Secondary colors
    vars['--secondary'] = `hsl(${themeColors.secondary.DEFAULT})`;
    vars['--secondary-foreground'] = `hsl(${themeColors.secondary.foreground})`;

    // Muted colors
    vars['--muted'] = `hsl(${themeColors.muted.DEFAULT})`;
    vars['--muted-foreground'] = `hsl(${themeColors.muted.foreground})`;

    // Accent colors
    vars['--accent'] = `hsl(${themeColors.accent.DEFAULT})`;
    vars['--accent-foreground'] = `hsl(${themeColors.accent.foreground})`;

    // Background and foreground
    vars['--background'] = `hsl(${themeColors.background})`;
    vars['--foreground'] = `hsl(${themeColors.foreground})`;

    // Card colors
    vars['--card'] = `hsl(${themeColors.card.DEFAULT})`;
    vars['--card-foreground'] = `hsl(${themeColors.card.foreground})`;

    // UI elements
    vars['--border'] = `hsl(${themeColors.border})`;
    vars['--input'] = `hsl(${themeColors.input})`;
    vars['--ring'] = `hsl(${themeColors.ring})`;

    // Destructive colors
    vars['--destructive'] = `hsl(${themeColors.destructive.DEFAULT})`;
    vars['--destructive-foreground'] = `hsl(${themeColors.destructive.foreground})`;

    return vars;
  }, [themeColors]);

  if (!mounted) {
    return <div className="opacity-0">{children}</div>;
  }

  return (
    <RemixThemeContext.Provider value={{ mode }}>
      <div
        className="remix-theme-scope min-h-screen"
        style={{ ...cssVariables, fontFamily: 'var(--font-family)' }}
        data-theme={mode}
      >
        {children}
      </div>
    </RemixThemeContext.Provider>
  );
}
