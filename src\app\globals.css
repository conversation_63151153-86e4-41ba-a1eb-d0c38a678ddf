@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Safari iOS viewport height fix */
:root {
  /* Fallback for browsers that don't support dvh */
  --viewport-height: 100vh;
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
}

/* Use dynamic viewport height when supported (Safari 15.4+) */
@supports (height: 100dvh) {
  :root {
    --viewport-height: 100dvh;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  /* This targets iOS Safari specifically */
  :root {
    --viewport-height: calc(100vh - var(--safe-area-inset-bottom));
  }

  /* Use dvh if available on iOS Safari 15.4+ */
  @supports (height: 100dvh) {
    :root {
      --viewport-height: 100dvh;
    }
  }
}


/* Remix theme scope - font family setup */
.remix-theme-scope {
  font-family: var(--font-family, 'Zilla Slab', serif) !important;
}
.remix-theme-scope .font-secondary,
.remix-theme-scope [data-font="secondary"] {
  font-family: var(--font-secondary, 'Inter', sans-serif) !important;
}

/* Optionally, you can add a utility for primary font override if needed */
.remix-theme-scope .font-primary,
.remix-theme-scope [data-font="primary"] {
  font-family: var(--font-primary, 'Zilla Slab', serif) !important;
}

:root {
  /* Light theme colors */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: #fafafa;
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Dark theme colors */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: #ededed;
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom sidebar positioning to account for music player */
  [data-slot="sidebar-container"] {
    bottom: var(--music-player-height, 0px) !important;
    height: calc(var(--viewport-height, 100vh) - var(--music-player-height, 0px)) !important;
  }

  /* Also adjust the inner sidebar container */
  [data-slot="sidebar-inner"] {
    height: calc(var(--viewport-height, 100vh) - var(--music-player-height, 0px)) !important;
  }

  /* Safari iOS specific fixes for music player positioning */
  .music-player-container {
    /* Ensure music player stays above Safari's bottom UI */
    padding-bottom: var(--safe-area-inset-bottom, 0px);
    /* Use safe viewport height */
    position: relative;
    z-index: 50;
  }

  /* Fix for Safari iOS viewport height issues */
  .safari-safe-height {
    height: var(--viewport-height, 100vh);
    min-height: var(--viewport-height, 100vh);
  }

  /* Ensure content doesn't get hidden behind Safari's UI */
  .safari-safe-container {
    height: var(--viewport-height, 100vh);
    padding-bottom: var(--safe-area-inset-bottom, 0px);
  }

  /* iOS Safari specific music player positioning */
  @supports (-webkit-touch-callout: none) {
    .music-player-container {
      /* Add extra bottom padding for iOS Safari */
      padding-bottom: max(var(--safe-area-inset-bottom, 0px), 8px);
    }

    /* Ensure the layout container uses safe viewport height */
    .safari-safe-height {
      height: var(--viewport-height, calc(100vh - var(--safe-area-inset-bottom, 0px)));
      min-height: var(--viewport-height, calc(100vh - var(--safe-area-inset-bottom, 0px)));
    }
  }

  /* Remix theme scoping - override global theme variables within this scope */
  .remix-theme-scope {
    /* The CSS custom properties are applied via inline styles from RemixThemeProvider */
    /* This ensures the remix theme only affects elements within this container */
  }
}
