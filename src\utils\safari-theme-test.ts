/**
 * Safari Theme Compatibility Test Utility
 * 
 * This utility helps test and debug theme-related issues in Safari browsers.
 * It provides functions to detect Safari, test CSS custom property support,
 * and validate theme color values.
 */

// Detect if the current browser is Safari
export function isSafari(): boolean {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  const isSafariUA = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS/.test(userAgent);
  
  return isSafariUA;
}

// Detect if the current browser is Safari on iOS
export function isSafariIOS(): boolean {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafariUA = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS/.test(userAgent);
  
  return isIOS && isSafariUA;
}

// Test if CSS custom properties are working correctly
export function testCSSCustomProperties(): {
  supported: boolean;
  hslSupported: boolean;
  rgbSupported: boolean;
  details: string[];
} {
  if (typeof window === 'undefined') {
    return {
      supported: false,
      hslSupported: false,
      rgbSupported: false,
      details: ['Server-side rendering - cannot test CSS properties']
    };
  }

  const details: string[] = [];
  
  // Create a test element
  const testElement = document.createElement('div');
  testElement.style.display = 'none';
  document.body.appendChild(testElement);
  
  try {
    // Test basic CSS custom property support
    testElement.style.setProperty('--test-color', 'red');
    testElement.style.color = 'var(--test-color)';
    const basicSupported = getComputedStyle(testElement).color === 'red' || 
                           getComputedStyle(testElement).color === 'rgb(255, 0, 0)';
    
    details.push(`Basic CSS custom properties: ${basicSupported ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
    
    // Test HSL color support
    testElement.style.setProperty('--test-hsl', 'hsl(0, 100%, 50%)');
    testElement.style.color = 'var(--test-hsl)';
    const hslColor = getComputedStyle(testElement).color;
    const hslSupported = hslColor === 'rgb(255, 0, 0)' || hslColor.includes('255, 0, 0');
    
    details.push(`HSL color support: ${hslSupported ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
    details.push(`HSL computed value: ${hslColor}`);
    
    // Test RGB color support
    testElement.style.setProperty('--test-rgb', 'rgb(0, 255, 0)');
    testElement.style.color = 'var(--test-rgb)';
    const rgbColor = getComputedStyle(testElement).color;
    const rgbSupported = rgbColor === 'rgb(0, 255, 0)' || rgbColor.includes('0, 255, 0');
    
    details.push(`RGB color support: ${rgbSupported ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
    details.push(`RGB computed value: ${rgbColor}`);
    
    return {
      supported: basicSupported,
      hslSupported,
      rgbSupported,
      details
    };
  } catch (error) {
    details.push(`Error during testing: ${error}`);
    return {
      supported: false,
      hslSupported: false,
      rgbSupported: false,
      details
    };
  } finally {
    document.body.removeChild(testElement);
  }
}

// Test theme color values in the current context
export function testThemeColors(): {
  success: boolean;
  colors: Record<string, string>;
  issues: string[];
} {
  if (typeof window === 'undefined') {
    return {
      success: false,
      colors: {},
      issues: ['Server-side rendering - cannot test theme colors']
    };
  }

  const issues: string[] = [];
  const colors: Record<string, string> = {};
  
  // List of theme colors to test
  const themeColorNames = [
    'primary',
    'primary-foreground',
    'secondary',
    'secondary-foreground',
    'background',
    'foreground',
    'card',
    'border',
    'input',
    'ring'
  ];
  
  const rootStyles = getComputedStyle(document.documentElement);
  
  themeColorNames.forEach(colorName => {
    const cssVarName = `--${colorName}`;
    const value = rootStyles.getPropertyValue(cssVarName).trim();
    
    if (!value) {
      issues.push(`Missing CSS variable: ${cssVarName}`);
    } else {
      colors[colorName] = value;
      
      // Check if the value looks like a valid color
      if (!value.match(/^(rgb|hsl|#|\w+)/)) {
        issues.push(`Invalid color format for ${cssVarName}: ${value}`);
      }
    }
  });
  
  return {
    success: issues.length === 0,
    colors,
    issues
  };
}

// Generate a comprehensive browser and theme compatibility report
export function generateCompatibilityReport(): {
  browser: {
    isSafari: boolean;
    isSafariIOS: boolean;
    userAgent: string;
  };
  cssSupport: ReturnType<typeof testCSSCustomProperties>;
  themeTest: ReturnType<typeof testThemeColors>;
  recommendations: string[];
} {
  const browser = {
    isSafari: isSafari(),
    isSafariIOS: isSafariIOS(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'
  };
  
  const cssSupport = testCSSCustomProperties();
  const themeTest = testThemeColors();
  
  const recommendations: string[] = [];
  
  if (browser.isSafari && !cssSupport.hslSupported) {
    recommendations.push('Use RGB color values instead of HSL for better Safari compatibility');
  }
  
  if (browser.isSafariIOS) {
    recommendations.push('Consider using viewport height fixes for iOS Safari');
  }
  
  if (!cssSupport.supported) {
    recommendations.push('CSS custom properties are not supported - consider using fallback colors');
  }
  
  if (themeTest.issues.length > 0) {
    recommendations.push('Theme color issues detected - check CSS variable definitions');
  }
  
  return {
    browser,
    cssSupport,
    themeTest,
    recommendations
  };
}

// Console logging utility for debugging
export function logCompatibilityReport(): void {
  if (typeof console === 'undefined') return;
  
  const report = generateCompatibilityReport();
  
  console.group('🎨 Theme Compatibility Report');
  
  console.group('🌐 Browser Detection');
  console.log('Safari:', report.browser.isSafari);
  console.log('Safari iOS:', report.browser.isSafariIOS);
  console.log('User Agent:', report.browser.userAgent);
  console.groupEnd();
  
  console.group('🎯 CSS Support');
  console.log('Custom Properties:', report.cssSupport.supported);
  console.log('HSL Colors:', report.cssSupport.hslSupported);
  console.log('RGB Colors:', report.cssSupport.rgbSupported);
  console.log('Details:', report.cssSupport.details);
  console.groupEnd();
  
  console.group('🎨 Theme Colors');
  console.log('Success:', report.themeTest.success);
  console.log('Colors:', report.themeTest.colors);
  if (report.themeTest.issues.length > 0) {
    console.warn('Issues:', report.themeTest.issues);
  }
  console.groupEnd();
  
  if (report.recommendations.length > 0) {
    console.group('💡 Recommendations');
    report.recommendations.forEach(rec => console.log('•', rec));
    console.groupEnd();
  }
  
  console.groupEnd();
}
