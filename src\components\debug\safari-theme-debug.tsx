'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { generateCompatibilityReport, logCompatibilityReport } from '@/utils/safari-theme-test';

interface CompatibilityReport {
  browser: {
    isSafari: boolean;
    isSafariIOS: boolean;
    userAgent: string;
  };
  cssSupport: {
    supported: boolean;
    hslSupported: boolean;
    rgbSupported: boolean;
    details: string[];
  };
  themeTest: {
    success: boolean;
    colors: Record<string, string>;
    issues: string[];
  };
  recommendations: string[];
}

/**
 * Safari Theme Debug Component
 * 
 * This component helps debug theme-related issues in Safari browsers.
 * It displays browser detection, CSS support information, and theme color tests.
 * 
 * Usage:
 * - Add this component to any page to test theme compatibility
 * - Check the console for detailed compatibility report
 * - Use the visual indicators to verify theme colors are working
 */
export function SafariThemeDebug() {
  const [report, setReport] = useState<CompatibilityReport | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const compatibilityReport = generateCompatibilityReport();
    setReport(compatibilityReport);
    
    // Log detailed report to console
    logCompatibilityReport();
  }, []);

  if (!mounted || !report) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Safari Theme Debug</CardTitle>
          <CardDescription>Loading compatibility report...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎨 Safari Theme Debug
            {report.browser.isSafari && (
              <Badge variant="secondary">Safari Detected</Badge>
            )}
          </CardTitle>
          <CardDescription>
            Theme compatibility testing and debugging for Safari browsers
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Browser Detection */}
      <Card>
        <CardHeader>
          <CardTitle>🌐 Browser Detection</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <span>Safari Browser:</span>
              <Badge variant={report.browser.isSafari ? "default" : "secondary"}>
                {report.browser.isSafari ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Safari iOS:</span>
              <Badge variant={report.browser.isSafariIOS ? "default" : "secondary"}>
                {report.browser.isSafariIOS ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            <strong>User Agent:</strong> {report.browser.userAgent}
          </div>
        </CardContent>
      </Card>

      {/* CSS Support */}
      <Card>
        <CardHeader>
          <CardTitle>🎯 CSS Support</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between">
              <span>Custom Properties:</span>
              <Badge variant={report.cssSupport.supported ? "default" : "destructive"}>
                {report.cssSupport.supported ? "Supported" : "Not Supported"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>HSL Colors:</span>
              <Badge variant={report.cssSupport.hslSupported ? "default" : "destructive"}>
                {report.cssSupport.hslSupported ? "Supported" : "Not Supported"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>RGB Colors:</span>
              <Badge variant={report.cssSupport.rgbSupported ? "default" : "destructive"}>
                {report.cssSupport.rgbSupported ? "Supported" : "Not Supported"}
              </Badge>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Test Details:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {report.cssSupport.details.map((detail, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  {detail}
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Theme Colors Test */}
      <Card>
        <CardHeader>
          <CardTitle>🎨 Theme Colors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Theme Test Status:</span>
            <Badge variant={report.themeTest.success ? "default" : "destructive"}>
              {report.themeTest.success ? "Success" : "Issues Found"}
            </Badge>
          </div>

          {report.themeTest.issues.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-destructive">Issues:</h4>
              <ul className="text-sm text-destructive space-y-1">
                {report.themeTest.issues.map((issue, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span>⚠️</span>
                    {issue}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-medium">Current Theme Colors:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              {Object.entries(report.themeTest.colors).map(([name, value]) => (
                <div key={name} className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="font-mono">--{name}:</span>
                  <span className="font-mono text-muted-foreground">{value}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visual Theme Test */}
      <Card>
        <CardHeader>
          <CardTitle>🎭 Visual Theme Test</CardTitle>
          <CardDescription>
            These elements should display with the correct theme colors
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button variant="default">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="destructive">Destructive Button</Button>
            <Button variant="link">Link Button</Button>
          </div>
          
          <div className="space-y-2">
            <div className="p-4 bg-primary text-primary-foreground rounded">
              Primary background with primary foreground text
            </div>
            <div className="p-4 bg-secondary text-secondary-foreground rounded">
              Secondary background with secondary foreground text
            </div>
            <div className="p-4 bg-muted text-muted-foreground rounded">
              Muted background with muted foreground text
            </div>
            <div className="p-4 bg-card text-card-foreground border rounded">
              Card background with card foreground text and border
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {report.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>💡 Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {report.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-primary">💡</span>
                  <span className="text-sm">{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground text-center">
            Check the browser console for detailed compatibility information.
            This debug component can be removed in production.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
